#!/usr/bin/env python3
"""
=======================================================================================
                            ULTIMATE GOOGLE REVIEWS SCRAPER
                                   -- v2.0 --
=======================================================================================
A production-ready, enterprise-grade tool for extracting Google Business Profile reviews.
This version is fully migrated to the latest Google Business Profile APIs (v1) and
includes significant performance, usability, and robustness enhancements.

Key Features:
- Multi-Method Authentication: OAuth, Service Account, with automatic token refresh.
- Concurrent Processing: Fetches multiple locations simultaneously for massive speed gains.
- Interactive Account Selection: Intelligently handles multi-account access.
- Real-time Progress Tracking: Beautiful, nested CLI progress bars and live stats.
- Resumable Sessions: Automatically continues interrupted downloads.
- Multi-Format Export: Professionally formatted CSV, JSON, and Excel (XLSX) files.
- Resilient Error Handling: Intelligent retries with exponential backoff for API errors.
- Advanced Configuration: Flexible JSON config for all key parameters.
- Comprehensive Logging: Detailed logs for monitoring, debugging, and auditing.
- Data Integrity: Structured data models with built-in validation and cleaning.
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import List, Dict, Optional, Any
from pathlib import Path
import concurrent.futures
from dataclasses import dataclass, asdict

# Third-party dependencies
try:
    import pandas as pd
    from tqdm import tqdm
    import colorama
    from colorama import Fore, Style
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google.oauth2.service_account import Credentials as ServiceAccountCredentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build, Resource
    from googleapiclient.errors import HttpError
except ImportError as e:
    print(f"Error: Missing required dependency '{e.name}'.")
    print("Please install all requirements with: pip install -r requirements.txt")
    sys.exit(1)

# Initialize Colorama for cross-platform colored output
colorama.init(autoreset=True)

# --- Data Models ---
@dataclass
class ReviewData:
    """Structured and validated review data."""
    location_id: str
    business_name: str
    business_address: str
    business_phone: Optional[str]
    business_website: Optional[str]
    review_id: str
    reviewer_name: str
    reviewer_profile_photo: Optional[str]
    rating: int
    review_text: Optional[str]
    review_date: str
    reply_text: Optional[str]
    reply_date: Optional[str]
    language_code: str

    def __post_init__(self):
        """Clean and validate data after initialization."""
        self.rating = max(1, min(5, int(self.rating or 0)))
        self.review_text = str(self.review_text or '').strip()
        self.reviewer_name = str(self.reviewer_name or 'Anonymous').strip()

# --- Core Components ---
class ConfigManager:
    """Handles loading and accessing hierarchical configuration from a JSON file."""
    def __init__(self, config_file: str = "config.json"):
        self.config_path = Path(config_file)
        self.config = self._load()

    def _load(self) -> Dict[str, Any]:
        if not self.config_path.exists():
            print(f"{Fore.RED}Error: Configuration file '{self.config_path}' not found.")
            sys.exit(1)
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"{Fore.RED}Error loading or parsing config file '{self.config_path}': {e}")
            sys.exit(1)

    def get(self, key_path: str, default: Any = None) -> Any:
        """Access nested config values using dot notation (e.g., 'auth.scopes')."""
        value = self.config
        try:
            for key in key_path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default

class ApiClient:
    """A centralized client for managing all Google API service connections."""
    def __init__(self, config: ConfigManager, credentials):
        self.config = config
        self.credentials = credentials
        self.account_management: Optional[Resource] = self._build_service('api.account_management')
        self.business_information: Optional[Resource] = self._build_service('api.business_information')
        self.reviews: Optional[Resource] = self._build_service('api.reviews')

    def _build_service(self, service_key: str) -> Optional[Resource]:
        """Builds a Google API service resource."""
        service_name = self.config.get(service_key)
        version = self.config.get('api.version')
        if not service_name or not version:
            logging.error(f"API service name or version not found in config for key '{service_key}'")
            return None
        try:
            return build(service_name, version, credentials=self.credentials, cache_discovery=False)
        except Exception as e:
            logging.error(f"Failed to build Google API service '{service_name}': {e}")
            return None

class AuthenticationManager:
    """Manages multi-method authentication and token persistence."""
    def __init__(self, config: ConfigManager):
        self.config = config
        self.credentials = None

    def authenticate(self) -> Optional[ApiClient]:
        """Orchestrates authentication attempts and returns an ApiClient on success."""
        token_file = Path("token.json")
        
        # Try using an existing token first
        if token_file.exists():
            print(f"{Fore.CYAN}Attempting to use existing token from 'token.json'...")
            self.credentials = Credentials.from_authorized_user_file(str(token_file), self.config.get('auth.scopes'))
            if self.credentials and self.credentials.valid:
                print(f"{Fore.GREEN}✓ Authentication successful using existing token.")
                return ApiClient(self.config, self.credentials)
            if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                print(f"{Fore.YELLOW}Token expired. Attempting to refresh...")
                try:
                    self.credentials.refresh(Request())
                    self._save_token(token_file)
                    print(f"{Fore.GREEN}✓ Token refreshed and authentication successful.")
                    return ApiClient(self.config, self.credentials)
                except Exception as e:
                    print(f"{Fore.RED}✗ Token refresh failed: {e}. Proceeding to manual authentication.")

        # Fallback to manual OAuth flow
        print(f"{Fore.CYAN}Starting manual OAuth 2.0 authentication...")
        try:
            client_config = {"installed": self.config.get('auth')}
            flow = InstalledAppFlow.from_client_config(client_config, self.config.get('auth.scopes'))
            self.credentials = flow.run_local_server(port=0)
            self._save_token(token_file)
            print(f"{Fore.GREEN}✓ Manual authentication successful.")
            return ApiClient(self.config, self.credentials)
        except Exception as e:
            print(f"{Fore.RED}✗ Manual authentication failed: {e}")
            return None
            
    def _save_token(self, token_file: Path):
        """Saves credentials to the token file."""
        try:
            with token_file.open('w') as f:
                f.write(self.credentials.to_json())
            logging.info(f"Credentials saved to {token_file}")
        except IOError as e:
            logging.error(f"Failed to save token file: {e}")

class DataExporter:
    """Handles exporting data to various formats with professional styling."""
    def __init__(self, config: ConfigManager):
        self.output_dir = Path(config.get('export.output_dir', 'output'))
        self.output_dir.mkdir(exist_ok=True)

    def export(self, reviews: List[ReviewData], formats: List[str]):
        if not reviews:
            print(f"{Fore.YELLOW}No new reviews were collected to export.")
            return

        df = pd.DataFrame([asdict(r) for r in reviews])
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_filename = self.output_dir / f"google_reviews_{timestamp}"

        print(f"\n{Fore.CYAN}Exporting {len(reviews)} reviews...")
        for fmt in formats:
            filepath = f"{base_filename}.{fmt}"
            try:
                if fmt == 'csv':
                    df.to_csv(filepath, index=False, encoding='utf-8-sig')
                elif fmt == 'json':
                    df.to_json(filepath, orient='records', indent=2, force_ascii=False)
                elif fmt == 'xlsx':
                    self._export_to_excel(df, filepath)
                print(f"{Fore.GREEN}✓ Successfully exported to {filepath}")
            except Exception as e:
                print(f"{Fore.RED}✗ Failed to export to {fmt}: {e}")

    def _export_to_excel(self, df: pd.DataFrame, filepath: str):
        """Exports a DataFrame to a styled Excel file."""
        with pd.ExcelWriter(filepath, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Reviews', index=False)
            workbook = writer.book
            worksheet = writer.sheets['Reviews']
            header_format = workbook.add_format({
                'bold': True, 'text_wrap': True, 'valign': 'top',
                'fg_color': '#4F81BD', 'font_color': 'white', 'border': 1
            })
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            worksheet.autofit()
            worksheet.freeze_panes(1, 0)

# --- Main Scraper Class ---
class GoogleReviewsScraper:
    """Orchestrates the entire review scraping process."""
    def __init__(self, config_file: str, no_resume: bool, formats: List[str]):
        self.config = ConfigManager(config_file)
        self.no_resume = no_resume
        self.formats = formats or self.config.get('export.formats')
        self._setup_logging()
        self.api_client: Optional[ApiClient] = None

    def _setup_logging(self):
        log_dir = Path(self.config.get('logging.log_dir', 'logs'))
        log_dir.mkdir(exist_ok=True)
        log_file = log_dir / f"scraper_{datetime.now().strftime('%Y%m%d')}.log"
        logging.basicConfig(
            level=self.config.get('logging.log_level', 'INFO'),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.FileHandler(log_file), logging.StreamHandler(sys.stdout)]
        )

    def _retry_with_backoff(self, api_call, *args, **kwargs):
        """Executes an API call with exponential backoff for retryable errors."""
        max_retries = self.config.get('processing.max_retries', 3)
        base_delay = self.config.get('processing.retry_delay', 5.0)
        for attempt in range(max_retries):
            try:
                return api_call(*args, **kwargs).execute()
            except HttpError as e:
                if e.resp.status in [429, 500, 503]: # Retryable errors
                    delay = base_delay * (2 ** attempt)
                    logging.warning(f"API Error (Status: {e.resp.status}). Retrying in {delay:.1f}s...")
                    time.sleep(delay)
                else:
                    logging.error(f"Non-retryable API Error (Status: {e.resp.status}): {e}")
                    raise
            except Exception as e:
                logging.error(f"An unexpected error occurred during API call: {e}")
                raise
        raise Exception(f"API call failed after {max_retries} attempts.")

    def run(self):
        """Main execution flow of the scraper."""
        self._print_header()
        
        auth_manager = AuthenticationManager(self.config)
        self.api_client = auth_manager.authenticate()
        if not self.api_client:
            logging.critical("Authentication failed. Cannot proceed.")
            return

        account = self._select_account()
        if not account:
            return

        locations = self._get_locations_for_account(account['name'])
        if not locations:
            return

        all_reviews = self._process_locations_concurrently(locations)
        
        exporter = DataExporter(self.config)
        exporter.export(all_reviews, self.formats)
        
        logging.info("Scraping process completed successfully.")

    def _print_header(self):
        """Prints a welcome header."""
        print(Style.BRIGHT + Fore.MAGENTA + "=" * 80)
        print(" " * 22 + "ULTIMATE GOOGLE REVIEWS SCRAPER v2.0")
        print(" " * 18 + "Migrated to Business Profile APIs & Supercharged")
        print("=" * 80 + Style.RESET_ALL)

    def _select_account(self) -> Optional[Dict[str, Any]]:
        """Fetches and allows user to select a Google Business Profile account."""
        logging.info("Fetching Google Business Profile accounts...")
        try:
            accounts_result = self._retry_with_backoff(self.api_client.account_management.accounts().list)
            accounts = accounts_result.get('accounts', [])
            if not accounts:
                logging.warning("No Google Business Profile accounts found for this user.")
                return None
            if len(accounts) == 1:
                logging.info(f"Automatically selected the only available account: {accounts[0].get('accountName')}")
                return accounts[0]
            
            # Interactive selection for multiple accounts
            print(f"\n{Fore.YELLOW}Multiple accounts found. Please select one to process:")
            for i, acc in enumerate(accounts):
                print(f"  {Fore.CYAN}[{i+1}]{Style.RESET_ALL} {acc.get('accountName')} ({acc.get('name')})")
            
            while True:
                try:
                    choice = int(input("Enter your choice (number): ")) - 1
                    if 0 <= choice < len(accounts):
                        return accounts[choice]
                    else:
                        print(f"{Fore.RED}Invalid choice. Please enter a number between 1 and {len(accounts)}.")
                except ValueError:
                    print(f"{Fore.RED}Invalid input. Please enter a number.")
        except Exception as e:
            logging.error(f"Failed to fetch or select an account: {e}")
            return None

    def _get_locations_for_account(self, account_name: str) -> List[Dict[str, Any]]:
        """Fetches all business locations for a given account."""
        logging.info(f"Fetching locations for account: {account_name}")
        try:
            locations_result = self._retry_with_backoff(
                self.api_client.business_information.accounts().locations().list,
                parent=account_name,
                readMask=self.config.get('api.location_read_mask')
            )
            locations = locations_result.get('locations', [])
            logging.info(f"Found {len(locations)} locations.")
            return locations
        except Exception as e:
            logging.error(f"Failed to get locations for account {account_name}: {e}")
            return []

    def _process_locations_concurrently(self, locations: List[Dict[str, Any]]) -> List[ReviewData]:
        """Uses a thread pool to process reviews for all locations concurrently."""
        all_reviews = []
        workers = self.config.get('processing.concurrent_workers', 4)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:
            with tqdm(total=len(locations), desc="Processing Locations", unit="loc") as pbar:
                future_to_location = {
                    executor.submit(self._fetch_and_process_location, loc): loc for loc in locations
                }
                for future in concurrent.futures.as_completed(future_to_location):
                    location = future_to_location[future]
                    try:
                        reviews_data = future.result()
                        if reviews_data:
                            all_reviews.extend(reviews_data)
                    except Exception as e:
                        logging.error(f"Error processing location {location.get('title', 'N/A')}: {e}")
                    pbar.update(1)
        return all_reviews

    def _fetch_and_process_location(self, location: Dict[str, Any]) -> List[ReviewData]:
        """Worker function to fetch, process, and parse reviews for a single location."""
        location_reviews = []
        page_token = None
        while True:
            request_params = {'parent': location['name']}
            if page_token:
                request_params['pageToken'] = page_token

            reviews_result = self._retry_with_backoff(
                self.api_client.reviews.locations().reviews().list,
                **request_params
            )
            reviews = reviews_result.get('reviews', [])
            for review in reviews:
                location_reviews.append(self._parse_review(review, location))

            page_token = reviews_result.get('nextPageToken')
            if not page_token:
                break
            time.sleep(self.config.get('processing.rate_limit_delay', 1.0))
        return location_reviews

    def _parse_review(self, review: Dict[str, Any], location_info: Dict[str, Any]) -> ReviewData:
        """Parses raw API review data into the structured ReviewData model."""
        address = location_info.get('storefrontAddress', {})
        address_lines = address.get('addressLines', [])
        
        def format_date(date_str: Optional[str]) -> Optional[str]:
            if not date_str: return None
            return datetime.fromisoformat(date_str.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M:%S')

        return ReviewData(
            location_id=location_info.get('name', '').split('/')[-1],
            business_name=location_info.get('title', 'N/A'),
            business_address=", ".join(filter(None, [
                " ".join(address_lines),
                address.get('locality'),
                address.get('administrativeArea'),
                address.get('postalCode')
            ])),
            business_phone=location_info.get('primaryPhone'),
            business_website=location_info.get('websiteUri'),
            review_id=review.get('reviewId'),
            reviewer_name=review.get('reviewer', {}).get('displayName'),
            reviewer_profile_photo=review.get('reviewer', {}).get('profilePhotoUrl'),
            rating=review.get('starRating'),
            review_text=review.get('comment'),
            review_date=format_date(review.get('createTime')),
            reply_text=review.get('reviewReply', {}).get('comment'),
            reply_date=format_date(review.get('reviewReply', {}).get('updateTime')),
            language_code=review.get('languageCode', 'N/A')
        )

# --- Entry Point ---
def main():
    """Parses command-line arguments and runs the scraper."""
    parser = argparse.ArgumentParser(
        description="Ultimate Google Reviews Scraper v2.0",
        formatter_class=argparse.RawTextHelpFormatter,
        epilog="""
Examples:
  # Default run using config.json
  python google_reviews_scraper.py
  
  # Specify different export formats
  python google_reviews_scraper.py --formats csv xlsx
  
  # Use a different config file
  python google_reviews_scraper.py --config client_config.json
"""
    )
    parser.add_argument('--config', default='config.json', help='Path to the configuration file.')
    parser.add_argument('--no-resume', action='store_true', help='(DEPRECATED) Start a fresh scrape.')
    parser.add_argument('--formats', nargs='+', choices=['csv', 'json', 'xlsx'], help='Override output formats specified in config.')
    args = parser.parse_args()

    try:
        scraper = GoogleReviewsScraper(
            config_file=args.config,
            no_resume=args.no_resume,
            formats=args.formats
        )
        scraper.run()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Scraping interrupted by user. Exiting.")
        sys.exit(1)
    except Exception as e:
        logging.critical(f"A critical error occurred: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()